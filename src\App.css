/* Mobile-First CSS Reset and Base Styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  overflow-x: hidden !important;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  scroll-behavior: smooth;
  /* Improve text rendering on mobile */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent horizontal scrolling issues */
  min-height: 100vh;
  position: relative;
}

#root {
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  text-align: left !important;
  width: 100% !important;
  min-height: 100vh;
  position: relative;
}

/* CSS Custom Properties for Responsive Design */
:root {
  /* Responsive spacing scale */
  --space-xs: clamp(0.25rem, 0.5vw, 0.5rem);
  --space-sm: clamp(0.5rem, 1vw, 0.75rem);
  --space-md: clamp(0.75rem, 1.5vw, 1rem);
  --space-lg: clamp(1rem, 2vw, 1.5rem);
  --space-xl: clamp(1.5rem, 3vw, 2rem);
  --space-2xl: clamp(2rem, 4vw, 3rem);

  /* Responsive typography scale */
  --text-xs: clamp(0.75rem, 2vw, 0.875rem);
  --text-sm: clamp(0.875rem, 2.5vw, 1rem);
  --text-base: clamp(1rem, 3vw, 1.125rem);
  --text-lg: clamp(1.125rem, 3.5vw, 1.25rem);
  --text-xl: clamp(1.25rem, 4vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 5vw, 2rem);
  --text-3xl: clamp(2rem, 6vw, 2.5rem);
  --text-4xl: clamp(2.5rem, 7vw, 3rem);

  /* Line heights for better readability */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Touch target sizes */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;

  /* Responsive breakpoints */
  --mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1023px;
  --desktop-min: 1024px;

  /* Z-index scale for proper layering */
  --z-background: -1;
  --z-base: 0;
  --z-content: 1;
  --z-header: 10;
  --z-navigation: 100;
  --z-dropdown: 200;
  --z-overlay: 1000;
  --z-modal: 1500;
  --z-notification: 1600;
  --z-tooltip: 1700;
  --z-debug: 10000;

  /* Common gradient patterns */
  --gradient-primary: linear-gradient(135deg, #591C28 0%, #6E8C65 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-heritage-gold) 0%, #fbbf24 100%);
  --gradient-accent: linear-gradient(135deg, #591C28, var(--color-heritage-gold));
  --gradient-success: linear-gradient(135deg, #6E8C65 0%, #355E3B 100%);
  --gradient-danger: linear-gradient(135deg, #591C28 0%, #4A1520 100%);

  /* Brand colors - Black Community Heritage & Empowerment */
  --color-primary: #B8860B; /* Rich gold - excellence, achievement, and prosperity */
  --color-secondary: #591C28; /* Dark maroon - strength, heritage, and dignity */
  --color-accent-green: #6E8C65; /* Muted green - growth, prosperity, and hope */
  --color-accent-purple: #4B0082; /* Royal purple - dignity, wisdom, and nobility */
  --color-accent-bronze: #CD7F32; /* Bronze - heritage and strength */
  --color-accent-emerald: #6E8C65; /* Muted green - renewal and growth */

  /* Cultural heritage colors */
  --color-heritage-gold: #FFD700; /* Bright gold - celebration and success */
  --color-heritage-burgundy: #591C28; /* Burgundy - strength and heritage */
  --color-heritage-forest: #355E3B; /* Forest green - stability and growth */
  --color-heritage-royal: #663399; /* Royal purple - leadership and wisdom */

  /* Supporting colors */
  --color-dark: #1a1a1a;
  --color-light: #fff;
  --color-gray-light: #f8f9fa;
  --color-gray-medium: #6c757d;
  --color-gray-dark: #343a40;

  /* Animation and transition settings */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Border radius scale */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;

  /* Shadow scale */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
}

/* Animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Base Interactive Element Styles */
.btn-base, button, .nav-btn, .action-btn, .submit-btn, .cancel-btn, .confirm-btn,
.create-campaign-btn, .new-request-btn, .start-discussion-btn, .close-btn,
.naroop-reaction-btn, .naroop-delete-btn, .quick-action-btn {
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  font-family: inherit;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: var(--touch-target-min);
  user-select: none;
}

.btn-base:focus-visible, button:focus-visible, .nav-btn:focus-visible, .action-btn:focus-visible,
.quick-action-btn:focus-visible, .cancel-btn:focus-visible, .confirm-btn:focus-visible,
.submit-btn:focus-visible, .create-campaign-btn:focus-visible, .new-request-btn:focus-visible,
.start-discussion-btn:focus-visible, .cta-btn:focus-visible, .naroop-reaction-btn:focus-visible,
.naroop-admin-btn:focus-visible, .naroop-privacy-btn:focus-visible, .naroop-logout-btn:focus-visible,
.naroop-account-link:focus-visible, .naroop-signup-btn:focus-visible, .naroop-login-btn:focus-visible,
.close-modal-btn:focus-visible {
  outline: 3px solid #F7D046; /* High contrast yellow outline for accessibility */
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(247, 208, 70, 0.2);
}

/* Loading spinner for lazy-loaded components */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-2xl) var(--space-lg);
  min-height: 200px;
  color: var(--color-text-primary);
  font-size: var(--text-base);
}

.loading-spinner::before {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-accent-emerald);
  border-top: 3px solid transparent;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Boundary Styles */
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--space-lg);
  background: linear-gradient(135deg, #f7fafc 0%, var(--color-accent-emerald) 100%);
}

.error-boundary-content {
  max-width: 500px;
  text-align: center;
  background: white;
  padding: var(--space-2xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.error-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-lg);
}

.error-boundary h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-size: var(--text-2xl);
}

.error-boundary p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-btn {
  padding: var(--space-sm) var(--space-xl);
  border-radius: var(--radius-md);
  font-weight: 500;
}

.error-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-btn-primary {
  background: var(--color-accent-emerald);
  color: white;
}

.error-btn-primary:hover:not(:disabled) {
  background: var(--color-accent-emerald-dark);
  transform: translateY(-1px);
}

.error-btn-secondary {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.error-btn-secondary:hover {
  background: var(--color-background-tertiary);
  transform: translateY(-1px);
}

.error-details {
  margin-top: 24px;
  text-align: left;
}

.error-details summary {
  cursor: pointer;
  color: var(--color-text-secondary);
  margin-bottom: 12px;
}

.error-stack {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  color: #666;
}

/* App.css */

/* Prevent white screen issues */
html, body {
  background: linear-gradient(135deg, #f7fafc 0%, var(--color-accent-emerald) 100%);
  min-height: 100vh;
}

#root {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7fafc 0%, var(--color-accent-emerald) 100%);
}

.naroop-main {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f7fafc 0%, var(--color-accent-emerald) 100%);
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  position: relative;
  /* Ensure proper stacking context */
  isolation: isolate;
}

/* Background decoration */
.naroop-main::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: -1;
  /* Prevent background from interfering with content */
  will-change: auto;
}

.naroop-header {
  background: linear-gradient(90deg, var(--color-dark) 0%, var(--color-heritage-burgundy) 60%, var(--color-heritage-gold) 100%);
  color: #fff;
  padding: 2rem 1rem 1.5rem 1rem;
  text-align: center;
  border-radius: 0 0 24px 24px;
  box-shadow: 0 4px 16px rgba(26,26,46,0.08);
}

.naroop-header h1 {
  font-size: var(--text-4xl);
  margin: 0 0 var(--space-xs) 0;
  letter-spacing: 0.1em;
  font-weight: 800;
  line-height: var(--leading-tight);
}

.naroop-header h2 {
  font-size: var(--text-xl);
  font-weight: 400;
  margin: 0 0 var(--space-lg) 0;
  letter-spacing: 0.05em;
  line-height: var(--leading-normal);
}

.naroop-hero {
  /* Mobile-first hero styles - minimized */
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, var(--color-heritage-burgundy) 0%, var(--color-dark) 40%, var(--color-heritage-gold) 100%);
  color: #FDFBF5; /* Light cream text for better contrast */
  padding: var(--space-lg) var(--space-md) var(--space-md) var(--space-md);
  margin: 0 0 var(--space-lg) 0;
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.8s ease-out;
  overflow: hidden;
  min-height: 25vh;
  text-align: center;
}

/* Hero background pattern */
.naroop-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="hexagon" width="60" height="60" patternUnits="userSpaceOnUse"><polygon points="30,2 52,16 52,44 30,58 8,44 8,16" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hexagon)"/></svg>');
  pointer-events: none;
}

.naroop-hero h1 {
  font-size: var(--text-2xl);
  margin: 0 0 var(--space-xs) 0;
  letter-spacing: 0.1em;
  font-weight: 800;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  position: relative;
  z-index: 2;
  color: #FDFBF5; /* Solid cream color for better readability */
  line-height: var(--leading-tight);
}

.naroop-hero h2 {
  font-size: var(--text-lg);
  font-weight: 400;
  margin: 0 0 var(--space-md) 0;
  letter-spacing: 0.05em;
  position: relative;
  z-index: 2;
  color: #FDFBF5; /* Solid cream color for better readability */
  text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
  line-height: var(--leading-normal);
}

.naroop-mission {
  font-size: var(--text-base);
  max-width: min(600px, 90vw);
  margin: 0 auto;
  color: #FDFBF5; /* Solid cream color for better readability */
  line-height: 1.5;
  position: relative;
  z-index: 2;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
  animation: fadeInUp 0.8s ease-out 0.3s both;
  padding: 0 var(--space-sm);
}

/* Hero Header Layout */
.naroop-hero-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  flex-direction: column;
  gap: var(--space-md);
}

.naroop-hero-content {
  flex: 1;
  text-align: center;
  width: 100%;
}



/* Tablet and Desktop Hero Styles - minimized */
@media screen and (min-width: 768px) {
  .naroop-hero {
    padding: var(--space-xl) var(--space-lg) var(--space-lg) var(--space-lg);
    min-height: 30vh;
  }

  .naroop-hero h1 {
    font-size: var(--text-3xl);
  }

  .naroop-hero h2 {
    font-size: var(--text-xl);
  }

  .naroop-mission {
    font-size: var(--text-lg);
  }

  .naroop-hero-header {
    flex-direction: row;
    gap: var(--space-lg);
  }
}

@media screen and (min-width: 1024px) {
  .naroop-hero {
    padding: var(--space-xl) var(--space-xl) var(--space-lg) var(--space-xl);
    min-height: 35vh;
  }

  .naroop-hero h1 {
    font-size: var(--text-4xl);
  }
}

.naroop-content {
  /* Mobile-first content layout */
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: var(--space-xl);
  margin: 0 0 var(--space-xl) 0;
  width: 100%;
  padding: 0 var(--space-md);
  position: relative;
  z-index: 1;
}

.naroop-form-col {
  flex: none;
  width: 100%;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.naroop-stories {
  flex: 1;
  width: 100%;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* Tablet Content Layout */
@media screen and (min-width: 768px) {
  .naroop-content {
    flex-direction: row;
    gap: var(--space-2xl);
    padding: 0 var(--space-xl);
  }

  .naroop-form-col {
    flex: 0 0 450px;
    animation: slideInLeft 0.8s ease-out 0.2s both;
  }

  .naroop-stories {
    flex: 1;
    animation: slideInRight 0.8s ease-out 0.4s both;
  }
}

/* Desktop Content Layout */
@media screen and (min-width: 1024px) {
  .naroop-content {
    gap: 2.5rem;
    padding: 0 2rem;
  }

  .naroop-form-col {
    flex: 0 0 550px;
  }
}

/* Large Desktop Content Layout - Even wider for better usability */
@media screen and (min-width: 1440px) {
  .naroop-content {
    gap: 3rem;
    padding: 0 3rem;
  }

  .naroop-form-col {
    flex: 0 0 650px;
  }
}

.naroop-stories h3 {
  font-size: 1.8rem;
  color: #222;
  margin: 0 0 1.5rem 0;
  font-weight: 700;
  text-align: center;
  position: relative;
}

.naroop-stories h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-heritage-crimson), var(--color-heritage-gold));
  border-radius: 2px;
}

.naroop-featured-quote {
  margin: 0 0 2rem 0;
  background: linear-gradient(90deg, var(--color-heritage-gold) 0%, var(--color-heritage-crimson) 100%);
  color: #222;
  padding: 1.5rem 2rem;
  font-size: 1.3rem;
  font-weight: 600;
  text-align: center;
  letter-spacing: 0.01em;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.8s ease-out 0.1s both;
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.3);
  border-radius: 8px;
  margin-left: 2rem;
  margin-right: 2rem;
  transform: perspective(1000px) rotateX(2deg);
}

.naroop-featured-quote::before {
  content: '"';
  font-size: 4rem;
  position: absolute;
  left: 1rem;
  top: -0.5rem;
  color: rgba(34, 34, 34, 0.2);
  font-family: Georgia, serif;
}

.naroop-featured-quote::after {
  content: '"';
  font-size: 4rem;
  position: absolute;
  right: 1rem;
  bottom: -1rem;
  color: rgba(34, 34, 34, 0.2);
  font-family: Georgia, serif;
}

.naroop-resources {
  background: #fff;
  margin: 2rem 0 0 0;
  padding: 2rem;
  font-size: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.8s ease-out 0.5s both;
  box-shadow: 0 8px 32px rgba(26,26,46,0.1);
  border-radius: 16px;
  margin-left: 2rem;
  margin-right: 2rem;
}

.naroop-resources h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #1a1a2e;
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
}

.naroop-resources h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-heritage-crimson), var(--color-heritage-gold));
  border-radius: 2px;
}

.naroop-resources ul {
  display: flex;
  gap: 2rem;
  list-style: none;
  padding: 0;
  margin: 0;
  justify-content: center;
  flex-wrap: wrap;
}

.naroop-resources li {
  margin: 0;
  font-size: 1rem;
}

.naroop-resources a {
  color: var(--color-heritage-crimson);
  text-decoration: none;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  display: block;
  position: relative;
  overflow: hidden;
}

.naroop-resources a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  transition: left 0.5s;
}

.naroop-resources a:hover::before {
  left: 100%;
}

.naroop-resources a:hover {
  background: linear-gradient(90deg, var(--color-heritage-burgundy), var(--color-heritage-gold));
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.3);
}

.naroop-about {
  background: #222;
  color: #fff;
  margin: 2rem;
  padding: 2rem;
  font-size: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.8s ease-out 0.6s both;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.naroop-about h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--color-heritage-gold);
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
}

.naroop-about h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-heritage-gold), var(--color-heritage-crimson));
  border-radius: 2px;
}

.naroop-about p {
  line-height: 1.6;
  max-width: 600px;
}

.naroop-story-list {
  margin-top: 1.5rem;
  flex: 1 1 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.naroop-story-placeholder {
  background: #f8f9fa;
  border: 2px dashed #e9ecef;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
  animation: pulse 2s infinite;
}

.naroop-story {
  /* Mobile-first story card styles */
  background: #f3f6fa;
  border-radius: 12px;
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: 0 4px 12px rgba(26,26,46,0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
  width: 100%;
}

.naroop-story::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--color-heritage-crimson), var(--color-heritage-gold));
  transition: width 0.3s ease;
}

.naroop-story:hover::before {
  width: 8px;
}

.naroop-story:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(26,26,46,0.15);
}

.naroop-story h4 {
  margin: 0 0 0.5em 0;
  color: #222;
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.3;
}

.naroop-story-topic {
  display: inline-block;
  background: linear-gradient(90deg, #2d2d2d, #444);
  color: var(--color-heritage-gold);
  border-radius: 20px;
  padding: 0.3em 0.8em;
  font-size: 0.85em;
  margin-bottom: 0.8em;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(45, 45, 45, 0.3);
  transition: transform 0.2s ease;
}

.naroop-story-topic:hover {
  transform: scale(1.05);
}

.naroop-story p {
  margin: 0.8em 0 0.5em 0;
  color: #333;
  line-height: 1.6;
}

.naroop-story-img {
  max-width: 100%;
  border-radius: 8px;
  margin-top: 1rem;
  box-shadow: 0 4px 16px rgba(26,26,46,0.12);
  transition: transform 0.3s ease;
}

.naroop-story-img:hover {
  transform: scale(1.02);
}

.naroop-story-form {
  /* Mobile-first form styles */
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(26,26,46,0.1);
  padding: var(--space-lg);
  margin-bottom: 0;
  margin: 0 var(--space-md) var(--space-xl) var(--space-md);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  position: relative;
  overflow: hidden;
  width: calc(100% - 2 * var(--space-md));
  max-width: 100%;
}

.naroop-story-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-heritage-crimson), var(--color-heritage-gold));
}

.naroop-story-form h4 {
  margin: 0 0 0.5em 0;
  color: #222;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: center;
  position: relative;
}

.naroop-story-form h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-heritage-crimson), var(--color-heritage-gold));
  border-radius: 1px;
}

.naroop-story-form label {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  color: #1a1a2e;
  gap: 0.5em;
  margin-bottom: 0.5em;
  position: relative;
}

.naroop-story-form input[type="text"],
.naroop-story-form textarea,
.naroop-story-form select {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: var(--space-md) var(--space-lg);
  font-size: max(16px, var(--text-base)); /* Prevents zoom on iOS */
  background: #f7fafc;
  color: #222;
  transition: all 0.3s ease;
  font-family: inherit;
  width: 100%;
  min-height: var(--touch-target-min);
  -webkit-appearance: none;
  appearance: none;
}

.naroop-story-form input[type="text"]:focus,
.naroop-story-form textarea:focus,
.naroop-story-form select:focus {
  outline: none;
  border-color: var(--color-heritage-crimson);
  background: #fff;
  box-shadow: 0 0 0 3px rgba(230, 57, 70, 0.1);
  transform: translateY(-1px);
}

.naroop-story-form input[type="file"] {
  margin-top: 0.5em;
  padding: 0.5em;
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  background: #f7fafc;
  transition: all 0.3s ease;
}

.naroop-story-form input[type="file"]:hover {
  border-color: var(--color-heritage-crimson);
  background: #fff;
}

.naroop-story-form button {
  background: linear-gradient(90deg, var(--color-heritage-crimson) 0%, var(--color-heritage-gold) 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-lg);
  font-weight: 700;
  cursor: pointer;
  margin-top: var(--space-lg);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(230, 57, 70, 0.3);
  position: relative;
  overflow: hidden;
  width: 100%;
  min-height: var(--touch-target-comfortable);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.naroop-story-form button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.naroop-story-form button:hover::before {
  left: 100%;
}

.naroop-story-form button:hover {
  background: linear-gradient(90deg, var(--color-heritage-gold) 0%, var(--color-heritage-crimson) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(230, 57, 70, 0.4);
}

.naroop-story-form button:active {
  transform: translateY(0);
}

/* Additional CSS for enhanced form features */

.naroop-story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.naroop-story-date {
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Form validation states */
.naroop-story-form input.error,
.naroop-story-form textarea.error {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.naroop-story-form input.success,
.naroop-story-form textarea.success {
  border-color: #28a745;
  background-color: #f0fff4;
}

.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: '⚠️';
}

.success-message {
  color: #28a745;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.character-count {
  font-size: 0.8rem;
  color: #6c757d;
  text-align: right;
  margin-top: 0.25rem;
}

.naroop-story-form small {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled button state */
.naroop-story-form button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.naroop-story-form button:disabled:hover {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

/* Enhanced quote transition */
.naroop-featured-quote {
  transition: all 0.5s ease-in-out;
}

/* Scroll to Top Button */
.naroop-scroll-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, var(--color-heritage-crimson), var(--color-heritage-gold));
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(230, 57, 70, 0.3);
  transition: all 0.3s ease;
  z-index: var(--z-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 0.3s ease;
  /* Ensure button doesn't interfere with other content */
  isolation: isolate;
}

.naroop-scroll-to-top:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(230, 57, 70, 0.4);
  background: linear-gradient(135deg, var(--color-heritage-gold), var(--color-heritage-crimson));
}

.naroop-scroll-to-top:active {
  transform: translateY(-2px);
}

/* Toast Notifications */
.naroop-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: #fff;
  color: #333;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: var(--z-notification);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  font-weight: 600;
  max-width: 300px;
  border-left: 4px solid #28a745;
  /* Ensure proper stacking */
  isolation: isolate;
  word-wrap: break-word;
}

.naroop-toast-success {
  border-left-color: #28a745;
}

.naroop-toast-info {
  border-left-color: #17a2b8;
}

.naroop-toast-warning {
  border-left-color: #ffc107;
}

.naroop-toast-error {
  border-left-color: #dc3545;
}

.naroop-toast-show {
  transform: translateX(0);
}

/* Mobile responsiveness for new features */
@media (max-width: 600px) {
  .naroop-scroll-to-top {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }
  
  .naroop-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }
}

/* Scroll animations - stories are visible by default */
@media (prefers-reduced-motion: no-preference) {
  .naroop-story {
    opacity: 1;
    transform: translateY(0);
  }

  .naroop-story.animate-in {
    transform: translateY(0);
    opacity: 1;
    transition: all 0.6s ease-out;
  }
}

/* Better focus indicators for accessibility */
.naroop-story-form input:focus,
.naroop-story-form textarea:focus,
.naroop-story-form select:focus,
.naroop-story-form button:focus {
  outline: 2px solid var(--color-heritage-burgundy);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .naroop-story {
    border: 2px solid #000;
  }
  
  .naroop-story-topic {
    border: 1px solid var(--color-heritage-gold);
  }
}

/* Print styles */
@media print {
  .naroop-story-form,
  .naroop-resources,
  .naroop-about {
    display: none;
  }
  
  .naroop-hero {
    background: #fff !important;
    color: #000 !important;
  }
  
  .naroop-story {
    break-inside: avoid;
    border: 1px solid #000;
    margin-bottom: 1rem;
  }
}



/* Additional responsive improvements */
@media (max-width: 600px) {
  .naroop-hero h1 {
    font-size: 2.2rem;
  }

  .naroop-hero h2 {
    font-size: 1.2rem;
  }

  .naroop-mission {
    font-size: 1.1rem;
  }

  .naroop-story-form {
    padding: var(--space-lg);
    margin: 0 var(--space-sm) var(--space-xl) var(--space-sm);
    width: calc(100% - 2 * var(--space-sm));
  }

  /* Ensure all interactive elements meet minimum touch target size on small screens */
  button, .nav-btn, .naroop-reaction-btn, input[type="submit"], input[type="button"],
  .action-btn, .close-btn, .cancel-btn, .confirm-btn, .submit-btn,
  .create-campaign-btn, .new-request-btn, .start-discussion-btn {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
    /* Ensure adequate spacing for touch */
    margin: var(--space-xs);
  }

  /* Improve text readability on very small screens */
  .naroop-story p, .naroop-story-content {
    font-size: max(16px, var(--text-base));
    line-height: 1.6;
  }

  /* Ensure proper spacing for touch interactions */
  .naroop-story-actions {
    gap: var(--space-md);
    flex-wrap: wrap;
  }
}

/* Tablet Story Form Styles */
@media screen and (min-width: 768px) {
  .naroop-story-form {
    padding: var(--space-xl);
    margin: 0 var(--space-md) var(--space-2xl) var(--space-md);
    width: calc(100% - 2 * var(--space-md));
    max-width: none;
    margin-left: 0;
    margin-right: 0;
  }

  .naroop-story-form button {
    width: auto;
    min-width: 200px;
    align-self: center;
  }
}

/* Desktop Story Form Styles */
@media screen and (min-width: 1024px) {
  .naroop-story-form {
    padding: 2rem;
    margin: 0 var(--space-lg) 2rem var(--space-lg);
    width: calc(100% - 2 * var(--space-lg));
    max-width: none;
    margin-left: 0;
    margin-right: 0;
  }
}

/* Large Desktop Story Form Styles */
@media screen and (min-width: 1440px) {
  .naroop-story-form {
    padding: 2.5rem;
    margin: 0 var(--space-xl) 2rem var(--space-xl);
    width: calc(100% - 2 * var(--space-xl));
  }

  .naroop-resources ul {
    gap: 0.5rem;
  }

  .naroop-resources a {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  /* Desktop Story Card Styles */
  .naroop-story {
    padding: 1.5rem;
  }

  .naroop-story-actions {
    gap: 0.5rem;
  }

  .naroop-reaction-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
}

/* Tablet Story Card Styles */
@media screen and (min-width: 768px) {
  .naroop-story {
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
  }

  .naroop-story-actions {
    gap: var(--space-md);
    flex-wrap: nowrap;
  }

  .naroop-reaction-btn {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-base);
  }
}

/* Featured Story Section */
.naroop-featured-story {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  margin: 0 2rem 2rem 2rem;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(26,26,46,0.1);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.naroop-featured-story::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-heritage-crimson), var(--color-heritage-gold), var(--color-heritage-crimson));
}

.naroop-featured-story h3 {
  text-align: center;
  color: #222;
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  position: relative;
}

.naroop-featured-story h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #e63946, #fbbf24);
  border-radius: 2px;
}

.naroop-featured-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.naroop-featured-info {
  flex: 1;
}

.naroop-featured-info h4 {
  color: #222;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0.5rem 0 1rem 0;
  line-height: 1.3;
}

.naroop-featured-info p {
  color: #333;
  line-height: 1.6;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.naroop-featured-image {
  flex: 0 0 200px;
  max-width: 200px;
}

.naroop-featured-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(26,26,46,0.15);
  transition: transform 0.3s ease;
}

.naroop-featured-image img:hover {
  transform: scale(1.05);
}

.naroop-reaction-display {
  display: inline-flex;
  gap: 1rem;
  align-items: center;
  background: linear-gradient(90deg, #f8f9fa, #fff);
  padding: 0.7rem 1.2rem;
  border-radius: 20px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(26,26,46,0.08);
  border: 1px solid #e9ecef;
}

/* Stories Header with Filter */
.naroop-stories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.naroop-topic-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.naroop-topic-filter label {
  font-weight: 600;
  color: #222;
  font-size: 0.9rem;
}

.naroop-filter-select {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  background: #fff;
  color: #222;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 180px;
}

.naroop-filter-select:focus {
  outline: none;
  border-color: #e63946;
  box-shadow: 0 0 0 3px rgba(230, 57, 70, 0.1);
}

.naroop-filter-select:hover {
  border-color: var(--color-heritage-gold);
  background: #fffdf7;
}

/* Mobile-First Story Actions/Reactions */
.naroop-story-actions {
  display: flex;
  gap: var(--space-sm);
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid #e9ecef;
  align-items: center;
  flex-wrap: wrap;
}

/* Delete Button Specific Styling */
.naroop-delete-btn {
  background-color: #ff4757 !important;
  color: white !important;
  border: 2px solid #ff3742 !important;
  transition: all 0.2s ease !important;
  margin-left: var(--space-sm) !important;
}

.naroop-delete-btn:hover {
  background-color: #ff3742 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3) !important;
}

.naroop-delete-btn:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(255, 71, 87, 0.3) !important;
}

.naroop-reaction-btn {
  background: transparent;
  border: 2px solid #6E8C65; /* Muted green outline */
  border-radius: 50px; /* Perfect pill shape */
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Consistent gap for icon-to-text spacing */
  font-weight: 600;
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  justify-content: center;
  color: #6E8C65;
  position: relative;
  overflow: hidden;
}

.naroop-reaction-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
  transition: left 0.5s;
}

.naroop-reaction-btn:hover::before {
  left: 100%;
}

.naroop-heart-btn:hover {
  background: #591C28; /* Dark maroon fill for heart reaction */
  border-color: #591C28;
  color: #FDFBF5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.25);
}

.naroop-clap-btn:hover {
  background: #F7D046; /* Yellow fill for clap reaction */
  border-color: #F7D046;
  color: #591C28;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(247, 208, 70, 0.25);
}

.naroop-share-btn:hover {
  background: #6E8C65; /* Green fill for share action */
  border-color: #6E8C65;
  color: #FDFBF5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 140, 101, 0.25);
}

.naroop-reaction-btn:active {
  transform: translateY(0);
  animation: pulse 0.3s;
}

/* Enhanced animations */
@keyframes heartPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes clapShake {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

.naroop-heart-btn:active {
  animation: heartPulse 0.4s ease;
}

.naroop-clap-btn:active {
  animation: clapShake 0.4s ease;
}

/* Reacted state styles */
.naroop-reaction-btn.reacted {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  border-color: var(--color-primary);
  color: var(--color-light);
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(184, 134, 11, 0.3);
  transform: translateY(-1px);
}

.naroop-heart-btn.reacted {
  background: var(--gradient-primary);
  border-color: var(--color-heritage-burgundy);
  color: var(--color-light);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.4);
}

.naroop-clap-btn.reacted {
  background: linear-gradient(135deg, var(--color-heritage-gold), #fbbf24);
  border-color: var(--color-heritage-gold);
  color: var(--color-dark);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
}

.naroop-reaction-btn.reacted:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(184, 134, 11, 0.4);
}

.naroop-heart-btn.reacted:hover {
  box-shadow: 0 6px 16px rgba(230, 57, 70, 0.5);
}

.naroop-clap-btn.reacted:hover {
  box-shadow: 0 6px 16px rgba(251, 191, 36, 0.5);
}

/* Mobile-First Community Stats Section */
.naroop-community-stats {
  background: linear-gradient(135deg, #222 0%, #2d2d2d 100%);
  margin: var(--space-md);
  padding: var(--space-lg);
  border-radius: 16px;
  color: #fff;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.naroop-community-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #fbbf24, #e63946, #fbbf24);
}

.naroop-stats-container {
  max-width: 1000px;
  margin: 0 auto;
}

.naroop-daily-fact {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(251, 191, 36, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.naroop-daily-fact h3 {
  color: var(--color-heritage-gold);
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.naroop-daily-fact p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  color: #f8f9fa;
}

.naroop-stats-grid {
  /* Mobile-first stats grid */
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
  margin-top: var(--space-lg);
}

.naroop-stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: var(--space-lg);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: var(--touch-target-comfortable);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.naroop-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
  transition: left 0.5s;
}

.naroop-stat-card:hover::before {
  left: 100%;
}

.naroop-stat-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(251, 191, 36, 0.3);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.naroop-stat-number {
  font-size: var(--text-3xl);
  font-weight: 900;
  color: var(--color-heritage-gold);
  margin-bottom: var(--space-sm);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

.naroop-stat-label {
  font-size: var(--text-xs);
  color: #d1d5db;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.naroop-popular-topic {
  grid-column: span 2;
}

/* Tablet Community Stats */
@media screen and (min-width: 768px) {
  .naroop-community-stats {
    margin: var(--space-xl);
    padding: var(--space-xl);
  }

  .naroop-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-lg);
  }

  .naroop-stat-card {
    padding: var(--space-xl);
  }
}

/* Desktop Community Stats */
@media screen and (min-width: 1024px) {
  .naroop-community-stats {
    margin: 2rem;
    padding: 2rem;
  }

  .naroop-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .naroop-stat-card {
    padding: 1.5rem;
  }

  .naroop-stat-number {
    font-size: 2.5rem;
  }

  .naroop-stat-label {
    font-size: 0.9rem;
  }
}

.naroop-topic-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--color-heritage-gold);
  margin: 0.5rem 0;
}

.naroop-topic-count {
  font-size: 0.85rem;
  color: #9ca3af;
  font-weight: 500;
}

/* Search and Filters */
.naroop-search-filters {
  background: #fff;
  margin: 0 2rem 2rem 2rem;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(26,26,46,0.08);
  border-top: 3px solid #e63946;
}

.naroop-search-container {
  margin-bottom: 1rem;
}

.naroop-search-box {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.naroop-search-input {
  width: 100%;
  padding: 0.8rem 3rem 0.8rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 25px;
  font-size: 1rem;
  background: #f7fafc;
  transition: all 0.3s ease;
}

.naroop-search-input:focus {
  outline: none;
  border-color: #e63946;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(230, 57, 70, 0.1);
}

.naroop-clear-search {
  position: absolute;
  right: 0.8rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
  padding: 0.2rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.naroop-clear-search:hover {
  background: #f0f0f0;
  color: #e63946;
}

.naroop-filters-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.naroop-filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.naroop-filter-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.naroop-bookmarks-toggle {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #555;
}

.naroop-bookmarks-toggle:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.naroop-bookmarks-toggle.active {
  background: linear-gradient(90deg, #e63946, #fbbf24);
  color: #fff;
  border-color: #e63946;
}

/* Story of the Month */
.naroop-story-of-month {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  margin: 0 2rem 2rem 2rem;
  padding: 2rem;
  border-radius: 16px;
  color: #fff;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

.naroop-story-of-month::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffd700, #ff6b35, #ffd700);
}

.naroop-month-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.naroop-month-header h3 {
  color: #ffd700;
  font-size: 1.8rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.naroop-voting-period {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 0.3rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.naroop-month-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.naroop-month-info {
  flex: 1;
}

.naroop-month-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ffd700, #ff6b35);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  margin-bottom: 1rem;
  font-weight: 700;
  color: #1a1a2e;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.naroop-crown {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

.naroop-month-info h4 {
  color: #fff;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0.5rem 0 1rem 0;
  line-height: 1.3;
}

.naroop-month-info p {
  color: #d1d5db;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.naroop-month-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.naroop-stat {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.naroop-month-image {
  flex: 0 0 200px;
  position: relative;
}

.naroop-month-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.naroop-winner-overlay {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(135deg, #ffd700, #ff6b35);
  color: #1a1a2e;
  padding: 0.5rem;
  border-radius: 8px;
  font-weight: 800;
  font-size: 0.8rem;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  transform: rotate(12deg);
}

.naroop-month-footer {
  margin-top: 1.5rem;
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.naroop-voting-info {
  color: #9ca3af;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

/* Enhanced Story Cards */
.naroop-story-meta-reading {
  margin: 0.5rem 0;
}

.naroop-reading-time {
  background: #f8f9fa;
  color: #666;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Enhanced Reaction Buttons */
.naroop-bookmark-btn:hover {
  background: linear-gradient(90deg, #8b5cf6, #a78bfa);
  border-color: #8b5cf6;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.naroop-bookmark-btn.bookmarked {
  background: linear-gradient(90deg, #8b5cf6, #a78bfa);
  border-color: #8b5cf6;
  color: #fff;
}

.naroop-vote-btn:hover {
  background: linear-gradient(90deg, #10b981, #34d399);
  border-color: #10b981;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

/* Responsive Design for New Features */
@media (max-width: 768px) {
  .naroop-search-filters {
    margin: 0 1rem 2rem 1rem;
    padding: 1rem;
  }
  
  .naroop-filters-row {
    flex-direction: column;
    align-items: stretch;
    gap: 0.8rem;
  }
  
  .naroop-filter-group {
    justify-content: space-between;
  }
  
  .naroop-story-of-month {
    margin: 0 1rem 2rem 1rem;
    padding: 1.5rem;
  }
  
  .naroop-month-content {
    flex-direction: column;
    text-align: center;
  }
  
  .naroop-month-image {
    flex: none;
    max-width: 250px;
  }
  
  .naroop-month-stats {
    justify-content: center;
  }
}

/* Popular Tags Section */
.naroop-popular-tags {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.naroop-popular-tags h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: #333;
  font-weight: 600;
}

.naroop-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.naroop-popular-tag {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.naroop-popular-tag:hover {
  background: linear-gradient(135deg, #e63946, #dc2626);
  color: white;
  border-color: #e63946;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(230, 57, 70, 0.2);
}

.dark-mode .naroop-popular-tags {
  border-top-color: #4a5568;
}

.dark-mode .naroop-popular-tags h4 {
  color: #f8f9fa;
}

.dark-mode .naroop-popular-tag {
  background: linear-gradient(135deg, #4a5568, #2d3748);
  border-color: #4a5568;
  color: #e2e8f0;
}

.dark-mode .naroop-popular-tag:hover {
  background: linear-gradient(135deg, var(--color-heritage-gold), var(--color-primary));
  border-color: var(--color-heritage-gold);
  color: #1a1a2e;
}

@media (max-width: 768px) {
  .naroop-popular-tags h4 {
    font-size: 0.9rem;
  }
  
  .naroop-popular-tag {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
  
  .naroop-tags-container {
    gap: 0.25rem;
  }
}

/* User Profile Modal */
.naroop-user-profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(89, 28, 40, 0.7); /* Dark maroon overlay */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 1rem;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.naroop-user-profile-content {
  background: #FDFBF5; /* Light cream background */
  border-radius: 16px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(89, 28, 40, 0.3);
  border: 1px solid rgba(89, 28, 40, 0.1);
}

.naroop-profile-header {
  background: var(--gradient-accent);
  color: white;
  padding: 2rem;
  text-align: center;
  position: relative;
}

.naroop-close-profile {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.naroop-close-profile:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.naroop-profile-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.naroop-profile-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
}

.naroop-profile-joined {
  opacity: 0.9;
  font-size: 0.9rem;
}

.naroop-profile-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  background: #f8f9fa;
}

.naroop-profile-stat {
  text-align: center;
}

.naroop-profile-stat .naroop-stat-number {
  font-size: 2rem;
  font-weight: 900;
  color: #e63946;
  margin-bottom: 0.25rem;
}

.naroop-profile-stat .naroop-stat-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.naroop-profile-badges {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.naroop-profile-badges h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.naroop-badges-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.naroop-badge {
  background: linear-gradient(135deg, var(--color-heritage-gold), var(--color-primary));
  color: #1a1a2e;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.naroop-profile-stories {
  padding: 1.5rem;
}

.naroop-profile-stories h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.naroop-profile-story-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid #e63946;
}

.naroop-profile-story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.naroop-profile-story-item h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.naroop-profile-story-item p {
  margin: 0 0 0.75rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.naroop-profile-story-reactions {
  font-size: 0.8rem;
  color: #888;
}

.dark-mode .naroop-user-profile-content {
  background: #2d3748;
  color: #f8f9fa;
}

.dark-mode .naroop-profile-stats {
  background: #1a202c;
}

.dark-mode .naroop-profile-story-item {
  background: #1a202c;
}

.dark-mode .naroop-profile-stories h4,
.dark-mode .naroop-profile-badges h4,
.dark-mode .naroop-profile-story-item h5 {
  color: #f8f9fa;
}

.dark-mode .naroop-profile-story-item p {
  color: #a0aec0;
}

@media (max-width: 600px) {
  .naroop-user-profile-modal {
    padding: 0.5rem;
  }
  
  .naroop-profile-header {
    padding: 1.5rem;
  }
  
  .naroop-profile-avatar {
    font-size: 3rem;
  }
  
  .naroop-profile-header h3 {
    font-size: 1.5rem;
  }
  
  .naroop-profile-stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem;
  }
  
  .naroop-profile-stat .naroop-stat-number {
    font-size: 1.5rem;
  }
}

/* Enhanced Story Export Modal */
.naroop-export-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 1rem;
}

.naroop-export-content {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.naroop-export-options {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.naroop-export-btn {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e63946;
  background: transparent;
  color: #e63946;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.naroop-export-btn:hover {
  background: #e63946;
  color: white;
}

.dark-mode .naroop-export-content {
  background: #2d3748;
  color: #f8f9fa;
}

.dark-mode .naroop-export-btn {
  border-color: var(--color-heritage-gold);
  color: var(--color-heritage-gold);
}

.dark-mode .naroop-export-btn:hover {
  background: #fbbf24;
  color: #1a1a2e;
}

/* Progress Tracker Component */
.naroop-progress-tracker {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.naroop-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.naroop-progress-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.naroop-toggle-details {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1rem;
  padding: 0.25rem;
  transition: all 0.3s ease;
}

.naroop-toggle-details:hover {
  color: #e63946;
  transform: scale(1.1);
}

.naroop-progress-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.naroop-progress-item {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, var(--color-heritage-gold), var(--color-primary));
  border-radius: 8px;
  color: #1a1a2e;
}

.naroop-progress-number {
  display: block;
  font-size: 1.8rem;
  font-weight: 900;
  margin-bottom: 0.25rem;
}

.naroop-progress-label {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.naroop-progress-details {
  border-top: 1px solid #e9ecef;
  padding-top: 1rem;
  margin-top: 1rem;
}

.naroop-next-achievement {
  margin-bottom: 1.5rem;
}

.naroop-next-achievement h5 {
  margin: 0 0 0.75rem 0;
  color: #333;
  font-size: 1rem;
}

.naroop-achievement-card {
  background: linear-gradient(135deg, #e63946, #dc2626);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.naroop-achievement-emoji {
  font-size: 2rem;
}

.naroop-achievement-name {
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.naroop-achievement-desc {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.naroop-achievement-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.naroop-progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.naroop-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-heritage-gold), var(--color-primary));
  border-radius: 3px;
  transition: width 0.5s ease;
}

.naroop-progress-text {
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.naroop-achievements-grid h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.naroop-achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.naroop-achievement-badge {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.naroop-achievement-badge.completed {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
  color: white;
  transform: scale(1.02);
}

.naroop-achievement-badge.locked {
  opacity: 0.5;
  background: #f1f3f4;
  border-color: #d1d5db;
}

.naroop-achievement-badge .naroop-achievement-emoji {
  font-size: 1.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

.naroop-achievement-badge .naroop-achievement-name {
  font-size: 0.8rem;
  font-weight: 600;
}

.naroop-achievement-check {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #fbbf24;
  color: #1a1a2e;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 900;
}

.dark-mode .naroop-progress-tracker {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  border-color: #4a5568;
}

.dark-mode .naroop-progress-header h4,
.dark-mode .naroop-next-achievement h5,
.dark-mode .naroop-achievements-grid h5 {
  color: #f8f9fa;
}

.dark-mode .naroop-progress-details {
  border-top-color: #4a5568;
}

.dark-mode .naroop-achievement-badge.locked {
  background: #1a202c;
  border-color: #4a5568;
  color: #718096;
}

@media (max-width: 768px) {
  .naroop-progress-summary {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .naroop-progress-item {
    padding: 0.75rem;
  }
  
  .naroop-progress-number {
    font-size: 1.5rem;
  }
  
  .naroop-achievement-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .naroop-achievements-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .naroop-achievement-badge {
    padding: 0.5rem;
  }
  
  .naroop-achievement-badge .naroop-achievement-emoji {
    font-size: 1.2rem;
  }
  
  .naroop-achievement-badge .naroop-achievement-name {
    font-size: 0.7rem;
  }
}

/* ===== NAROOP DESIGN SYSTEM IMPLEMENTATION ===== */
/*
 * NAROOP Design System Applied to Authenticated Interface
 *
 * Color Palette:
 * - Light Cream Background: #FDFBF5
 * - Dark Maroon Text: #591C28
 * - Muted Green Accents: #6E8C65
 * - Warm Yellow Highlights: #F7D046
 *
 * Button Design:
 * - All buttons are pill-shaped (border-radius: 50px)
 * - Default state: transparent background with colored outline
 * - Hover state: filled background with appropriate semantic colors
 *   - Yellow (#F7D046) for primary actions
 *   - Green (#6E8C65) for secondary actions
 *   - Maroon (#591C28) for destructive actions
 *
 * Icon Positioning:
 * - All icons positioned to the left of text
 * - Consistent 0.5rem gap between icon and text
 * - Proper flex alignment for visual consistency
 *
 * Accessibility:
 * - WCAG AA compliant contrast ratios maintained
 * - High contrast mode support
 * - Reduced motion support
 * - Proper focus indicators with yellow outline
 * - Touch-friendly minimum sizes (44px)
 *
 * Responsive Design:
 * - Mobile: 320-768px
 * - Tablet: 768-1024px
 * - Desktop: 1024px+
 */

/* Universal Button Icon Spacing */
button, .nav-btn, .action-btn, .quick-action-btn, .naroop-reaction-btn,
.submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn,
.cancel-btn, .confirm-btn, .cta-btn, .view-all-requests-btn {
  /* Ensure all buttons with icons have proper left-to-right layout */
  flex-direction: row;
  /* Ensure icons appear before text */
  text-align: left;
}

/* ===== NEW COMMUNITY FEATURES STYLES ===== */

/* Mobile-First Navigation Styles */
.naroop-main-nav {
  /* Mobile styles (default) */
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  padding: var(--space-md);
  background: #FDFBF5; /* Light cream background */
  margin-bottom: var(--space-xl);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(89, 28, 40, 0.1);
  position: relative;
  border: 1px solid rgba(89, 28, 40, 0.1);
}

/* Mobile hamburger menu toggle - only visible on mobile */
.nav-toggle {
  display: block;
  background: transparent;
  border: 2px solid #591C28; /* Dark maroon outline */
  color: #591C28;
  font-size: var(--text-xl);
  padding: var(--space-sm);
  cursor: pointer;
  align-self: flex-end;
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  border-radius: 50px; /* Perfect pill shape */
  transition: all 0.3s ease;
}

/* Hide hamburger menu on tablet and larger screens */
@media screen and (min-width: 768px) {
  .nav-toggle {
    display: none !important;
  }
}

/* Hide hamburger menu on tablet and larger screens - higher specificity */
@media screen and (min-width: 768px) {
  .nav-toggle {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}

.nav-toggle:hover,
.nav-toggle:focus {
  background: #591C28; /* Dark maroon fill on hover */
  color: #FDFBF5;
  transform: translateY(-1px);
  outline: 2px solid #591C28;
  outline-offset: 2px;
}

/* Additional media query to ensure hamburger is hidden on larger screens */
@media screen and (min-width: 768px) {
  .nav-toggle:hover,
  .nav-toggle:focus {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}

.nav-menu {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.nav-menu.open {
  max-height: 80vh; /* Use viewport height for better mobile support */
  overflow-y: auto; /* Enable scrolling when content exceeds height */
  padding-bottom: var(--space-md); /* Add padding for better scrolling experience */
}

.nav-btn {
  /* Mobile-first button styles */
  padding: var(--space-sm) var(--space-lg); /* Reduced vertical padding for more compact pills */
  background: transparent;
  border: 2px solid #6E8C65; /* Muted green outline */
  border-radius: 50px; /* Perfect pill shape */
  color: #6E8C65;
  font-weight: 600;
  font-size: var(--text-base);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: flex;
  flex-direction: row; /* Explicitly set horizontal layout */
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  min-height: var(--touch-target-min);
  text-align: left; /* Left align for icon-text layout */
  gap: 0.5rem; /* Consistent gap for icon-to-text spacing */
  white-space: nowrap; /* Prevent text wrapping */
  line-height: 1.2; /* Compact line height */
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(110, 140, 101, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover {
  background: #6E8C65; /* Green fill on hover */
  color: #FDFBF5;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(110, 140, 101, 0.25);
}

/* Navigation button subtitle - REMOVED as per design requirements */

.nav-btn:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

.nav-btn.active {
  background: #F7D046; /* Yellow fill for active state */
  color: #591C28;
  border-color: #F7D046;
  box-shadow: 0 4px 15px rgba(247, 208, 70, 0.3);
  transform: translateY(-2px);
}

.nav-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 3px;
  background: linear-gradient(90deg, #e63946, #fbbf24);
  border-radius: 2px;
  animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  0% {
    box-shadow: 0 0 5px rgba(230, 57, 70, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(230, 57, 70, 0.8);
  }
}

/* Tablet Navigation Styles */
@media screen and (min-width: 768px) {
  .naroop-main-nav {
    flex-direction: row;
    justify-content: center;
    gap: var(--space-md);
    padding: var(--space-lg);
  }

  /* Multiple approaches to ensure hamburger menu is completely hidden */
  .naroop-main-nav .nav-toggle,
  .nav-toggle {
    display: none !important; /* Completely hide hamburger menu on larger screens */
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
  }

  .nav-menu {
    display: flex !important; /* Always show navigation menu on larger screens */
    flex-direction: row;
    gap: var(--space-md);
    max-height: none;
    overflow: visible;
    background: none; /* Remove mobile background styling */
    border: none; /* Remove mobile border styling */
    backdrop-filter: none; /* Remove mobile backdrop filter */
    margin-top: 0; /* Remove mobile margin */
    padding: 0; /* Remove mobile padding */
  }

  .nav-btn {
    padding: var(--space-sm) var(--space-lg); /* Compact padding for tablet */
    font-size: var(--text-sm);
    min-width: auto;
    flex-direction: row; /* Ensure horizontal layout on tablet */
    white-space: nowrap; /* Prevent wrapping on tablet */
  }
}

/* Desktop Navigation Styles */
@media screen and (min-width: 1024px) {
  .naroop-main-nav {
    gap: var(--space-lg);
    padding: var(--space-xl);
  }

  /* Multiple approaches to ensure hamburger menu is completely hidden on desktop */
  .naroop-main-nav .nav-toggle,
  .nav-toggle {
    display: none !important; /* Ensure hamburger is completely hidden on desktop */
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
  }

  .nav-menu {
    display: flex !important; /* Always show navigation menu on desktop */
    gap: var(--space-lg);
    max-height: none !important; /* Override any mobile max-height restrictions */
    overflow: visible !important; /* Override any mobile overflow restrictions */
    background: none !important; /* Remove any mobile background styling */
    border: none !important; /* Remove any mobile border styling */
    backdrop-filter: none !important; /* Remove any mobile backdrop filter */
    margin-top: 0 !important; /* Remove any mobile margin */
    padding: 0 !important; /* Remove any mobile padding */
  }

  .nav-btn {
    padding: 0.75rem 1.5rem;
    font-size: var(--text-base);
  }
}

/* Navigation separator for visual distinction */
.nav-separator {
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  margin: var(--space-md) 0;
  border-radius: 1px;
  position: relative;
}



/* Enhanced Kids Zone Navigation Button */
.kids-nav-btn {
  background: linear-gradient(135deg, #ff6b6b, #feca57) !important;
  color: white !important;
  text-decoration: none !important;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.kids-nav-btn:hover,
.kids-nav-btn:focus {
  background: linear-gradient(135deg, #ff5252, #ffb74d) !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.kids-nav-btn::after {
  content: '✨';
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 1rem;
  animation: sparkle 2s ease-in-out infinite;
  pointer-events: none;
}

@keyframes sparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: rotate(180deg) scale(1.2);
    opacity: 1;
  }
}

/* Pulse animation for important buttons */
.task-management-btn, .messaging-btn, .notifications-btn {
  position: relative;
}

.task-management-btn:hover, .messaging-btn:hover, .notifications-btn:hover {
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Mobile-First Economic Empowerment Styles */
.economic-empowerment {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-lg);
}

.economic-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  padding: 0 var(--space-md);
}

.economic-header h2 {
  color: #2c3e50;
  margin-bottom: var(--space-sm);
  font-size: var(--text-2xl);
}

.goal-templates, .goals-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-lg);
  margin: var(--space-xl) 0;
}

.goal-template-card, .goal-card {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.goal-template-card:hover, .goal-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.goal-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin: 1rem 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.3s ease;
}

/* Community Dialogue Styles */
.community-dialogue {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.topic-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.perspective-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
}

.perspective-tag {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: none;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.perspective-tag.selected {
  color: white;
  font-weight: 600;
}

/* Community Support Styles */
.community-support {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.emergency-banner {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.emergency-contacts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.emergency-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.category-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Community Activism Styles */
.community-activism {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.activism-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  color: #7f8c8d;
  margin-top: 0.5rem;
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
}

.category-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.campaigns-list {
  display: grid;
  gap: 1.5rem;
  margin: 2rem 0;
}

.campaign-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.campaign-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.support-btn, .external-link-btn, .share-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.support-btn {
  background: #27ae60;
  color: white;
}

.support-btn.supported {
  background: #2ecc71;
}

.external-link-btn {
  background: #3498db;
  color: white;
}

.share-btn {
  background: #f39c12;
  color: white;
}

/* Enhanced Mobile Navigation Styles */
@media (max-width: 768px) {
  .naroop-main-nav {
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
    z-index: var(--z-navigation); /* Ensure nav is above other content */
  }

  .nav-menu {
    /* Enhanced mobile menu styles */
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: var(--space-sm);
  }

  .nav-menu.open {
    /* Improved mobile menu when open */
    max-height: calc(100vh - 200px); /* Account for header and other elements */
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--space-sm);
    /* Custom scrollbar for mobile */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  .nav-menu.open::-webkit-scrollbar {
    width: 4px;
  }

  .nav-menu.open::-webkit-scrollbar-track {
    background: transparent;
  }

  .nav-menu.open::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }

  .nav-btn {
    padding: 0.75rem 1rem; /* Increased for better touch targets */
    font-size: 0.9rem;
    min-height: 44px; /* Ensure minimum touch target size */
    margin-bottom: 0.25rem;
  }



  .goal-templates, .goals-grid, .topics-grid, .categories-grid {
    grid-template-columns: 1fr;
  }

  .activism-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .category-buttons {
    justify-content: center;
  }

  .campaign-actions {
    justify-content: center;
  }
}

/* ===== MODERN FORM STYLES ===== */

/* Modern Form Container */
.request-form, .campaign-form, .discussion-form, .goal-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(26,26,46,0.1);
  padding: 2rem;
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(230, 57, 70, 0.1);
  transition: all 0.3s ease;
}

.request-form::before, .campaign-form::before, .discussion-form::before, .goal-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #e63946, #fbbf24);
}

.request-form:hover, .campaign-form:hover, .discussion-form:hover, .goal-form:hover {
  box-shadow: 0 12px 40px rgba(26,26,46,0.15);
  transform: translateY(-2px);
}

.request-form h4, .campaign-form h4, .discussion-form h4, .goal-form h4 {
  margin: 0 0 1rem 0;
  color: #222;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: center;
  position: relative;
}

.request-form h4::after, .campaign-form h4::after, .discussion-form h4::after, .goal-form h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #e63946, #fbbf24);
  border-radius: 2px;
}

/* Modern Form Inputs */
.form-input, .form-textarea, .form-select {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  background: #f8fafc;
  color: #2d3748;
  transition: all 0.3s ease;
  font-family: inherit;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  position: relative;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: #e63946;
  background: #fff;
  box-shadow: 0 0 0 4px rgba(230, 57, 70, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-input:hover, .form-textarea:hover, .form-select:hover {
  border-color: #cbd5e0;
  background: #fff;
}

.form-input::placeholder, .form-textarea::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.6;
}

.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 3rem;
}

.form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23e63946' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Form Fields */
.form-field {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;
}

/* Form Labels */
.form-label {
  display: block;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  letter-spacing: 0.025em;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

/* Modern Buttons */
.submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn {
  background: transparent;
  border: 2px solid #F7D046; /* Warm yellow outline for primary actions */
  color: #F7D046;
  border-radius: 50px; /* Perfect pill shape */
  padding: 0.875rem 1.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.025em;
  box-shadow: 0 4px 12px rgba(247, 208, 70, 0.2);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn::before, .create-campaign-btn::before, .new-request-btn::before, .start-discussion-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.submit-btn:hover::before, .create-campaign-btn:hover::before, .new-request-btn:hover::before, .start-discussion-btn:hover::before {
  left: 100%;
}

.submit-btn:hover, .create-campaign-btn:hover, .new-request-btn:hover, .start-discussion-btn:hover {
  background: #F7D046; /* Yellow fill on hover for primary actions */
  color: #591C28;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(247, 208, 70, 0.3);
}

.submit-btn:active, .create-campaign-btn:active, .new-request-btn:active, .start-discussion-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled, .create-campaign-btn:disabled, .new-request-btn:disabled, .start-discussion-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.6;
}

.submit-btn:disabled:hover, .create-campaign-btn:disabled:hover, .new-request-btn:disabled:hover, .start-discussion-btn:disabled:hover {
  background: #9ca3af;
  transform: none;
  box-shadow: none;
}

.cancel-btn {
  background: transparent;
  color: #591C28; /* Dark maroon for destructive/cancel actions */
  border: 2px solid #591C28;
  border-radius: 50px; /* Perfect pill shape */
  padding: 0.875rem 1.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.025em;
  min-width: 100px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn:hover {
  background: #591C28; /* Maroon fill on hover for destructive action */
  color: #FDFBF5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.25);
}

/* Character Count and Helper Text */
.character-count {
  font-size: 0.8rem;
  color: #6b7280;
  text-align: right;
  margin-top: 0.25rem;
  font-weight: 500;
}

.form-helper-text {
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 0.25rem;
  line-height: 1.4;
}

/* Form Validation States */
.form-input.error, .form-textarea.error, .form-select.error {
  border-color: #ef4444;
  background-color: #fef2f2;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success, .form-textarea.success, .form-select.success {
  border-color: #10b981;
  background-color: #f0fdf4;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-error-message {
  color: #ef4444;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.form-error-message::before {
  content: '⚠️';
  font-size: 0.9rem;
}

.form-success-message {
  color: #10b981;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.form-success-message::before {
  content: '✓';
  font-size: 0.9rem;
}

/* Loading States */
.submit-btn.loading, .create-campaign-btn.loading, .new-request-btn.loading, .start-discussion-btn.loading {
  position: relative;
  color: transparent;
}

.submit-btn.loading::after, .create-campaign-btn.loading::after, .new-request-btn.loading::after, .start-discussion-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Form Styles */
@media (max-width: 768px) {
  .request-form, .campaign-form, .discussion-form, .goal-form {
    padding: 1.5rem;
    margin: 1rem 0;
    gap: 1.25rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn, .cancel-btn {
    width: 100%;
    justify-content: center;
  }

  .form-input, .form-textarea, .form-select {
    padding: 0.875rem 1rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

@media (max-width: 480px) {
  .request-form, .campaign-form, .discussion-form, .goal-form {
    padding: 1.25rem;
    border-radius: 12px;
  }

  .request-form h4, .campaign-form h4, .discussion-form h4, .goal-form h4 {
    font-size: 1.2rem;
  }

  .form-input, .form-textarea, .form-select {
    border-radius: 8px;
    padding: 0.75rem;
  }

  .submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn, .cancel-btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.95rem;
    border-radius: 8px;
  }
}

/* Focus and Accessibility Improvements */
.form-input:focus-visible, .form-textarea:focus-visible, .form-select:focus-visible {
  outline: 2px solid #e63946;
  outline-offset: 2px;
}

.submit-btn:focus-visible, .create-campaign-btn:focus-visible, .new-request-btn:focus-visible, .start-discussion-btn:focus-visible, .cancel-btn:focus-visible {
  outline: 2px solid #e63946;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .form-input, .form-textarea, .form-select {
    border-width: 3px;
    border-color: #000;
  }

  .submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn {
    border: 2px solid #000;
  }

  .cancel-btn {
    border-width: 3px;
    border-color: #000;
  }
}

/* Perspective Tags */
.perspective-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.perspective-tag {
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  background: #f8fafc;
  color: #4a5568;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.perspective-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.perspective-tag.selected {
  color: #fff;
  font-weight: 600;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Goal Form Modal */
.goal-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 1rem;
}

.goal-form-modal .goal-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 2rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.goal-form-modal .goal-form h3 {
  margin: 0 0 1rem 0;
  color: #222;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
}

.goal-templates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.goal-template-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.goal-template-card:hover {
  border-color: #e63946;
  background: #fff;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(230, 57, 70, 0.15);
}

.goal-template-card:focus {
  outline: 2px solid #e63946;
  outline-offset: 2px;
}

.goal-template-card .goal-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.goal-template-card h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.goal-template-card p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

.goal-template-action {
  color: #e63946;
  font-size: 0.85rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.goal-template-card:hover .goal-template-action {
  opacity: 1;
}

/* Amount Input Styling */
.amount-input {
  max-width: 200px;
  margin-top: 0.5rem;
}

/* Support Request Card Styles */
.support-request-card {
  background: #fff;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.support-request-card:hover {
  border-color: #cbd5e0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.request-header h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  flex: 1;
}

.request-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.request-category-badge,
.request-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #fff;
  white-space: nowrap;
}

.request-category-badge {
  font-size: 0.75rem;
  opacity: 0.9;
}

.request-description {
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.request-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 1rem;
}

.request-actions {
  display: flex;
  gap: 0.75rem;
}

.view-responses-btn {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-responses-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.responses-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.responses-list {
  margin-bottom: 1rem;
}

.response-item {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.response-header strong {
  color: #2d3748;
}

.response-header span {
  color: #718096;
}

.response-item p {
  margin: 0;
  color: #4a5568;
  line-height: 1.4;
}

.add-response {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.response-input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.response-input:focus {
  outline: none;
  border-color: #e63946;
}

.respond-btn {
  background: #e63946;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.respond-btn:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.respond-btn:disabled {
  background: #cbd5e0;
  color: #9ca3af;
  cursor: not-allowed;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.requests-list::-webkit-scrollbar {
  width: 8px;
}

.requests-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.requests-list::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.requests-list::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.no-requests {
  text-align: center;
  padding: 3rem 1rem;
  color: #718096;
  font-style: italic;
}

.view-all-requests-btn {
  background: transparent;
  border: 2px solid #6E8C65; /* Muted green outline for secondary action */
  color: #6E8C65;
  padding: 1rem 2rem;
  border-radius: 50px; /* Perfect pill shape */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  width: 100%;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* Consistent gap for icon-to-text spacing */
}

.view-all-requests-btn:hover {
  background: #6E8C65; /* Green fill on hover for secondary action */
  color: #FDFBF5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 140, 101, 0.25);
}

.requests-title-section {
  flex: 1;
}

.large-list-notice {
  margin: 0.5rem 0 0 0;
  padding: 0.75rem 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
  font-size: 0.9rem;
  line-height: 1.4;
}

.requests-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Responsive Support Request Styles */
@media (max-width: 768px) {
  .request-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .request-badges {
    align-self: stretch;
  }

  .request-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .add-response {
    flex-direction: column;
    gap: 0.75rem;
  }

  .respond-btn {
    align-self: stretch;
  }
}

/* Milestone Modal Styles */
.milestone-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 1rem;
}

.milestone-modal-content {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.milestone-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.milestone-modal-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-modal-btn {
  background: transparent;
  border: 2px solid #591C28; /* Dark maroon outline */
  font-size: 1.2rem;
  cursor: pointer;
  color: #591C28;
  padding: 0.5rem;
  border-radius: 50%; /* Perfect circle */
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  min-width: 2.5rem;
  min-height: 2.5rem;
}

.close-modal-btn:hover {
  background: #591C28; /* Maroon fill on hover */
  color: #FDFBF5;
  transform: rotate(90deg);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.25);
}

.milestone-modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

.milestone-progress-summary {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
}

.progress-stats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.completed-count {
  color: #38a169;
  font-size: 1.5rem;
}

.total-count {
  color: #718096;
}

.all-milestones-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.all-milestones-list::-webkit-scrollbar {
  width: 6px;
}

.all-milestones-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.all-milestones-list::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.all-milestones-list::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.milestone-item-detailed {
  background: #fff;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.milestone-item-detailed:hover {
  border-color: #cbd5e0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.milestone-item-detailed.completed {
  background: #f0fff4;
  border-color: #9ae6b4;
}

.milestone-checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.milestone-checkbox-container input[type="checkbox"] {
  margin-top: 0.25rem;
  transform: scale(1.2);
}

.milestone-checkbox-label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  cursor: pointer;
  flex: 1;
}

.milestone-title {
  font-weight: 600;
  color: #2d3748;
  font-size: 1rem;
}

.milestone-description {
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.4;
}

.milestone-target {
  color: #e63946;
  font-size: 0.85rem;
  font-weight: 500;
  background: #fef2f2;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  align-self: flex-start;
}

.milestone-status {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.status-completed {
  color: #38a169;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-pending {
  color: #ed8936;
  font-weight: 500;
  font-size: 0.9rem;
}

.milestone-modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
}

.close-modal-btn.secondary {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: auto;
  height: auto;
}

.close-modal-btn.secondary:hover {
  background: #cbd5e0;
  color: #2d3748;
}

/* Virtualized List Styles */
.virtualized-list {
  position: relative;
}

.empty-list-message {
  text-align: center;
  padding: 3rem 1rem;
  color: #718096;
  font-style: italic;
}

.virtualization-indicator {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  color: #718096;
  text-align: center;
  z-index: 10;
}

.list-performance-monitor {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 1rem;
  text-align: center;
}

/* Smooth scrolling for better UX */
.virtualized-list,
.requests-list,
.all-milestones-list {
  scroll-behavior: smooth;
}

/* Loading states for large lists */
.large-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #718096;
}

.large-list-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #e63946;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.75rem;
}

/* Enhanced Tags Styles */
.tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.view-all-tags-btn {
  background: #f8fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-tags-btn:hover {
  background: #e2e8f0;
  color: #2d3748;
  border-color: #cbd5e0;
}

.tags-performance-notice {
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
  font-size: 0.9rem;
  text-align: center;
}

.naroop-tags-container {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.naroop-tags-container::-webkit-scrollbar {
  width: 6px;
}

.naroop-tags-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.naroop-tags-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.naroop-tags-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Confirmation Dialog Styles */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(89, 28, 40, 0.7); /* Dark maroon overlay */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-tooltip);
  padding: 1rem;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  /* Prevent white screen issues during modal transitions */
  opacity: 1;
  transition: opacity 0.2s ease-out;
}

.confirmation-overlay.closing {
  opacity: 0;
  pointer-events: none;
}

.confirmation-dialog {
  background: #FDFBF5; /* Light cream background */
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(89, 28, 40, 0.3);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: confirmationSlideIn 0.3s ease-out;
  border: 1px solid rgba(89, 28, 40, 0.1);
}

.confirmation-dialog.danger {
  border-top: 4px solid #591C28; /* Dark maroon for danger */
}

.confirmation-dialog.warning {
  border-top: 4px solid #F7D046; /* Yellow for warning */
}

.confirmation-dialog.info {
  border-top: 4px solid #6E8C65; /* Green for info */
}

.confirmation-dialog.undo {
  border-top: 4px solid #10b981;
  max-width: 400px;
}

.confirmation-header {
  display: flex;
  align-items: center;
  padding: 1.5rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.confirmation-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.confirmation-header h3 {
  flex: 1;
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.close-btn:hover:not(:disabled) {
  background: #f3f4f6;
  color: #374151;
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.confirmation-body {
  padding: 1rem 2rem 1.5rem 2rem;
}

.confirmation-body p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.5;
}

.confirmation-typing {
  margin-top: 1rem;
}

.confirmation-typing label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
}

.confirmation-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.confirmation-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.confirmation-actions {
  display: flex;
  gap: 1rem;
  padding: 1rem 2rem 1.5rem 2rem;
  justify-content: flex-end;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 50px; /* Perfect pill shape */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid;
  font-size: 0.9rem;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: transparent;
  color: #591C28; /* Dark maroon for cancel */
  border-color: #591C28;
}

.cancel-btn:hover:not(:disabled) {
  background: #591C28; /* Maroon fill on hover */
  color: #FDFBF5;
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.25);
}

.confirm-btn {
  background: transparent;
  color: #6E8C65; /* Muted green for confirm */
  border-color: #6E8C65;
}

.confirm-btn.danger {
  background: transparent;
  color: #591C28; /* Dark maroon for dangerous confirm */
  border-color: #591C28;
}

.confirm-btn.warning {
  background: transparent;
  color: #F7D046; /* Yellow for warning confirm */
  border-color: #F7D046;
}

.confirm-btn:hover:not(:disabled) {
  background: #6E8C65; /* Green fill on hover */
  color: #FDFBF5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(110, 140, 101, 0.25);
}

.confirm-btn.danger:hover:not(:disabled) {
  background: #591C28; /* Maroon fill on hover for dangerous action */
  color: #FDFBF5;
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.25);
}

.confirm-btn.warning:hover:not(:disabled) {
  background: #F7D046; /* Yellow fill on hover for warning */
  color: #591C28;
  box-shadow: 0 4px 12px rgba(247, 208, 70, 0.25);
}

.confirm-btn:disabled, .cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Undo Dialog Styles */
.undo-content {
  text-align: center;
  padding: 2rem;
}

.undo-icon {
  font-size: 3rem;
  color: #10b981;
  margin-bottom: 1rem;
}

.undo-content h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.undo-content p {
  margin: 0 0 1.5rem 0;
  color: #6b7280;
}

.undo-countdown {
  background: #f3f4f6;
  border-radius: 8px;
  height: 8px;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.countdown-bar {
  background: #10b981;
  height: 100%;
  transition: width 1s linear;
}

.undo-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.undo-btn, .dismiss-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.9rem;
}

.undo-btn {
  background: #10b981;
  color: #fff;
}

.undo-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.dismiss-btn {
  background: #f3f4f6;
  color: #374151;
}

.dismiss-btn:hover {
  background: #e5e7eb;
}

@keyframes confirmationSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Confirmation Dialog */
@media (max-width: 768px) {
  .confirmation-dialog {
    margin: 1rem;
    max-width: none;
  }

  .confirmation-header {
    padding: 1rem 1.5rem 0.75rem 1.5rem;
  }

  .confirmation-body {
    padding: 0.75rem 1.5rem 1rem 1.5rem;
  }

  .confirmation-actions {
    padding: 0.75rem 1.5rem 1rem 1.5rem;
    flex-direction: column-reverse;
  }

  .cancel-btn, .confirm-btn {
    width: 100%;
  }

  .undo-content {
    padding: 1.5rem;
  }

  .undo-actions {
    flex-direction: column;
  }

  .undo-btn, .dismiss-btn {
    width: 100%;
  }
}

/* Content Actions Styles */
.content-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-top: 0.75rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #6E8C65; /* Muted green outline */
  border-radius: 50px; /* Perfect pill shape */
  background: transparent;
  color: #6E8C65;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Increased gap for better icon spacing */
  min-height: 36px;
  font-weight: 500;
}

.action-btn:hover:not(:disabled) {
  background: #6E8C65; /* Green fill on hover */
  color: #FDFBF5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(110, 140, 101, 0.25);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  border-color: #9ca3af;
  color: #9ca3af;
}

.edit-btn:hover:not(:disabled) {
  background: #6E8C65; /* Green fill for edit action */
  color: #FDFBF5;
  border-color: #6E8C65;
}

.archive-btn:hover:not(:disabled) {
  background: #F7D046; /* Yellow fill for archive action */
  color: #591C28;
  border-color: #F7D046;
  box-shadow: 0 4px 12px rgba(247, 208, 70, 0.25);
}

.delete-btn:hover:not(:disabled) {
  background: #591C28; /* Maroon fill for destructive action */
  color: #FDFBF5;
  border-color: #591C28;
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.25);
}

/* Bulk Actions Styles */
.bulk-actions {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin: 1rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: bulkSlideIn 0.3s ease-out;
}

.bulk-info {
  color: #4a5568;
  font-weight: 500;
}

.bulk-buttons {
  display: flex;
  gap: 0.75rem;
}

.bulk-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bulk-btn.archive-btn {
  background: var(--color-accent-bronze);
  color: #fff;
}

.bulk-btn.archive-btn:hover:not(:disabled) {
  background: #d97706;
  transform: translateY(-1px);
}

.bulk-btn.delete-btn {
  background: #dc2626;
  color: #fff;
}

.bulk-btn.delete-btn:hover:not(:disabled) {
  background: #b91c1c;
  transform: translateY(-1px);
}

.bulk-btn.cancel-btn {
  background: #6b7280;
  color: #fff;
}

.bulk-btn.cancel-btn:hover:not(:disabled) {
  background: #4b5563;
  transform: translateY(-1px);
}

.bulk-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

@keyframes bulkSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Selection Styles */
.content-item-selectable {
  position: relative;
  transition: all 0.2s ease;
}

.content-item-selectable.selected {
  background: #eff6ff;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.content-selection-checkbox {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
}

.select-all-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #4a5568;
  cursor: pointer;
}

.select-all-checkbox input {
  width: 1rem;
  height: 1rem;
}

/* Responsive Content Actions */
@media (max-width: 768px) {
  .content-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .action-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .bulk-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .bulk-btn {
    flex: 1;
    min-width: 120px;
  }

  .content-selection-checkbox {
    top: 0.5rem;
    right: 0.5rem;
  }
}

/* Task Management Dashboard Styles */
.task-management-dashboard {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.dashboard-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-dashboard-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.close-dashboard-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.dashboard-content {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Scrollable content areas for dashboard */
.dashboard-content {
  overflow-y: auto;
  max-height: calc(90vh - 2rem);
}

.dashboard-content .stats-section,
.dashboard-content .recommendations-section,
.dashboard-content .actions-section {
  flex-shrink: 0;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.stats-section, .recommendations-section, .actions-section {
  padding: 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.stats-section h3, .recommendations-section h3, .actions-section h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
  background: #fff;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.recommendation-card.medium {
  border-left: 4px solid var(--color-accent-bronze);
}

.recommendation-card.low {
  border-left: 4px solid #10b981;
}

.recommendation-content h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.recommendation-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

.recommendation-action {
  background: #3b82f6;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.recommendation-action:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.recommendation-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: #fff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.action-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.action-content h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

.action-content p {
  margin: 0 0 1.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

.action-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #3b82f6;
  color: #fff;
}

.action-btn.primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.action-btn.danger {
  background: #dc2626;
  color: #fff;
}

.action-btn.danger:hover:not(:disabled) {
  background: #b91c1c;
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.secondary:hover:not(:disabled) {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.cleanup-info {
  padding: 1rem 2rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 0.9rem;
}

/* Responsive Task Management Dashboard */
@media (max-width: 768px) {
  .task-management-dashboard {
    padding: 0.5rem;
  }

  .dashboard-header {
    padding: 1rem 1.5rem;
  }

  .dashboard-header h2 {
    font-size: 1.25rem;
  }

  .stats-section, .recommendations-section, .actions-section {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .recommendation-card {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .recommendation-action {
    margin-top: 1rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .cleanup-info {
    padding: 1rem 1.5rem;
  }
}

/* Mobile-First Messaging System Styles */
.messaging-system {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-notification);
  padding: 0; /* Mobile-first: no padding */
}

.messaging-content {
  /* Mobile-first messaging content */
  background: #fff;
  border-radius: 0; /* Mobile: full screen */
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
  max-width: none; /* Mobile: full width */
  width: 100%;
  height: 100vh; /* Mobile: full height */
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Messaging content body - scrollable area */
.messaging-content-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.messaging-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 16px 16px 0 0;
  flex-shrink: 0;
}

.messaging-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unread-badge {
  background: #dc2626;
  color: #fff;
  border-radius: 50%;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 1.5rem;
  text-align: center;
}

.close-messaging-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.close-messaging-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.messaging-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
}

/* Conversations Sidebar */
.conversations-sidebar {
  width: 350px;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.search-section {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.search-btn {
  padding: 0.5rem;
  background: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.search-btn:hover {
  background: #2563eb;
}

.new-message-btn {
  width: 100%;
  padding: 0.75rem;
  background: #10b981;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-message-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* Allows flex child to shrink below content size */
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.conversation-item:hover {
  background: #f3f4f6;
}

.conversation-item.active {
  background: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.conversation-avatar {
  position: relative;
  margin-right: 1rem;
}

.avatar-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #374151;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.75rem;
  height: 0.75rem;
  background: #10b981;
  border: 2px solid #fff;
  border-radius: 50%;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.last-message {
  color: #6b7280;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  text-align: right;
}

.last-time {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* Search Results */
.search-results {
  padding: 1rem;
}

.search-results h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1rem;
}

.search-result-item {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-result-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.result-content {
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.result-time {
  color: #9ca3af;
  font-size: 0.8rem;
}

.clear-search-btn {
  width: 100%;
  padding: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 0.5rem;
}

.clear-search-btn:hover {
  background: #e5e7eb;
}

/* Messages Area */
.messages-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.messages-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #fff;
}

.chat-participant {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.participant-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #374151;
}

.participant-name {
  font-weight: 600;
  color: #1f2937;
}

.participant-status {
  font-size: 0.8rem;
  color: #6b7280;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: 0; /* Allows flex child to shrink below content size */
}

.message-item {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-item.sent {
  align-self: flex-end;
  align-items: flex-end;
}

.message-item.received {
  align-self: flex-start;
  align-items: flex-start;
}

.message-content {
  background: #f3f4f6;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  position: relative;
}

.message-item.sent .message-content {
  background: #3b82f6;
  color: #fff;
}

.message-item.deleted .message-content {
  background: #f9fafb;
  color: #9ca3af;
  font-style: italic;
}

.message-text {
  word-wrap: break-word;
}

.edited-indicator {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-left: 0.5rem;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.message-time {
  font-size: 0.7rem;
  color: #9ca3af;
}

.message-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.edit-message-btn, .delete-message-btn {
  background: none;
  border: none;
  font-size: 0.8rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.edit-message-btn:hover {
  background: rgba(59, 130, 246, 0.1);
}

.delete-message-btn:hover {
  background: rgba(220, 38, 38, 0.1);
}

.edit-message-form input {
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.5rem;
  width: 100%;
}

/* Message Input */
.message-input-area {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #fff;
}

.input-container {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 24px;
  resize: none;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.send-btn {
  padding: 0.75rem;
  background: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
}

.send-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

/* Empty State */
.no-conversation-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* Tablet Messaging Styles */
@media screen and (min-width: 768px) {
  .messaging-system {
    padding: var(--space-lg);
  }

  .messaging-content {
    border-radius: 16px;
    max-width: 900px;
    height: 80vh;
    max-height: calc(90vh - 2rem);
  }
}

/* Desktop Messaging Styles */
@media screen and (min-width: 1024px) {
  .messaging-system {
    padding: 1rem;
  }

  .messaging-content {
    max-width: 1200px;
  }
}

/* Mobile Messaging Styles */
@media screen and (max-width: 768px) {
  .conversations-sidebar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 20;
    background: #fff;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .conversations-sidebar.open {
    transform: translateX(0);
  }

  .messages-area {
    width: 100%;
  }

  .message-item {
    max-width: 85%;
  }

  .messaging-header {
    padding: 1rem;
  }
}

/* Dynamic Newsfeed Styles */
.newsfeed-system {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 1rem;
}

.newsfeed-content {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.newsfeed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.newsfeed-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-newsfeed-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.close-newsfeed-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.newsfeed-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
}

.feed-filters {
  display: flex;
  gap: 2rem;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f3f4f6;
  background: #f8fafc;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #fff;
  font-size: 0.9rem;
  cursor: pointer;
}

.feed-stats {
  padding: 1rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #f3f4f6;
}

.stats-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.feed-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 2rem;
}

.feed-item {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.feed-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feed-item.followed {
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
}

.feed-item-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.item-type-indicator {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #fff;
}

.item-meta {
  flex: 1;
}

.item-author {
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.followed-badge {
  background: #3b82f6;
  color: #fff;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.item-time {
  font-size: 0.8rem;
  color: #9ca3af;
}

.feed-item-content {
  margin-bottom: 1rem;
}

.item-title {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
}

.item-description {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.feed-item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.item-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.view-item-btn {
  background: #3b82f6;
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-item-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.empty-feed {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-feed h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.empty-feed p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Responsive Newsfeed */
@media (max-width: 768px) {
  .newsfeed-system {
    padding: 0.5rem;
  }

  .newsfeed-content {
    max-width: none;
    height: 100vh;
    border-radius: 0;
  }

  .newsfeed-header {
    padding: 1rem 1.5rem;
  }

  .newsfeed-header h2 {
    font-size: 1.25rem;
  }

  .feed-filters {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 1.5rem;
  }

  .feed-stats {
    padding: 1rem 1.5rem;
  }

  .stats-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .feed-list {
    padding: 1rem 1.5rem;
  }

  .feed-item {
    padding: 1rem;
  }

  .feed-item-header {
    gap: 0.75rem;
  }

  .item-type-indicator {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }

  .feed-item-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .item-stats {
    justify-content: center;
  }
}

/* Notification Center Styles */
.notification-center {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-notification);
  padding: 1rem;
}

.notification-content {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
  max-width: 600px;
  width: 100%;
  max-height: calc(90vh - 2rem);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.notification-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preferences-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.preferences-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.close-notification-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.close-notification-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.notification-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
}

.notification-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid #f3f4f6;
  background: #f8fafc;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  color: #6b7280;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.filter-tab.active {
  background: #3b82f6;
  color: #fff;
  border-color: #3b82f6;
}

.mark-all-read-btn {
  background: #10b981;
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mark-all-read-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.notifications-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 2rem;
  min-height: 0; /* Allows flex child to shrink below content size */
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  background: #f0f9ff;
  border-color: #93c5fd;
}

.notification-icon {
  font-size: 1.5rem;
  background: #f3f4f6;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-content-area {
  flex: 1;
  min-width: 0;
}

.notification-header-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.notification-title {
  margin: 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.priority-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.notification-time {
  color: #9ca3af;
  font-size: 0.8rem;
}

.notification-message {
  margin: 0;
  color: #4b5563;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.unread-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 0.5rem;
  height: 0.5rem;
  background: #3b82f6;
  border-radius: 50%;
}

.notification-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.delete-notification-btn {
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  color: #9ca3af;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.delete-notification-btn:hover {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.empty-notifications {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-notifications h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.empty-notifications p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Notification Preferences Styles */
.notification-preferences {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preferences-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.preferences-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-preferences-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.close-preferences-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.preferences-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.preference-section {
  margin-bottom: 2rem;
}

.preference-section h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

.preference-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.preference-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preference-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.preference-item input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
}

.preference-label {
  color: #374151;
  font-size: 0.9rem;
  font-weight: 500;
}

.quiet-hours-config {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.time-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.time-input-group label {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

.time-input-group input[type="time"] {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.preferences-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.cancel-btn, .save-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.save-btn {
  background: #3b82f6;
  color: #fff;
}

.save-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Responsive Notification Center */
@media (max-width: 768px) {
  .notification-center {
    padding: 0.5rem;
  }

  .notification-content, .notification-preferences {
    max-width: none;
    height: 100vh;
    border-radius: 0;
  }

  .notification-header, .preferences-header {
    padding: 1rem 1.5rem;
  }

  .notification-header h2, .preferences-header h2 {
    font-size: 1.25rem;
  }

  .notification-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    padding: 1rem 1.5rem;
  }

  .filter-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }

  .notifications-list {
    padding: 1rem 1.5rem;
  }

  .notification-item {
    padding: 1rem;
  }

  .notification-header-info {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .preferences-content {
    padding: 1.5rem;
  }

  .preference-grid {
    grid-template-columns: 1fr;
  }

  .quiet-hours-config {
    flex-direction: column;
  }

  .preferences-actions {
    padding: 1rem 1.5rem;
    flex-direction: column-reverse;
  }

  .cancel-btn, .save-btn {
    width: 100%;
  }
}

/* Enhanced Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .request-form, .campaign-form, .discussion-form, .goal-form,
  .form-input, .form-textarea, .form-select,
  .submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn, .cancel-btn,
  .perspective-tag,
  .naroop-story,
  .nav-btn,
  .naroop-reaction-btn,
  .quick-action-btn,
  .confirm-btn,
  .cta-btn,
  .naroop-admin-btn,
  .naroop-privacy-btn,
  .naroop-logout-btn,
  .naroop-account-link,
  .naroop-signup-btn,
  .naroop-login-btn,
  .close-modal-btn {
    transition: none !important;
    transform: none !important;
    animation: none !important;
  }

  .submit-btn::before, .create-campaign-btn::before, .new-request-btn::before, .start-discussion-btn::before,
  .nav-btn::before,
  .naroop-story::before,
  .quick-action-btn::before,
  .quick-action-btn.primary::before {
    display: none !important;
  }

  /* Disable hover transforms for reduced motion */
  .submit-btn:hover, .create-campaign-btn:hover, .new-request-btn:hover, .start-discussion-btn:hover,
  .cancel-btn:hover, .confirm-btn:hover, .quick-action-btn:hover, .cta-btn:hover,
  .nav-btn:hover, .naroop-reaction-btn:hover, .close-modal-btn:hover {
    transform: none !important;
  }
}

/* Enhanced Focus Management for Mobile */
@media (hover: none) and (pointer: coarse) {
  /* Mobile devices - enhance focus indicators */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus,
  a:focus,
  [tabindex]:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(230, 57, 70, 0.2);
  }

  /* Remove hover effects on touch devices */
  .nav-btn:hover,
  .naroop-reaction-btn:hover,
  .naroop-story:hover {
    transform: none;
    box-shadow: inherit;
  }
}

/* High Contrast Mode Enhanced Support */
@media (prefers-contrast: high) {
  .naroop-story,
  .naroop-story-form,
  .nav-btn,
  .naroop-reaction-btn,
  .quick-action-btn,
  .cancel-btn,
  .confirm-btn,
  .submit-btn,
  .create-campaign-btn,
  .new-request-btn,
  .start-discussion-btn,
  .cta-btn,
  .naroop-admin-btn,
  .naroop-privacy-btn,
  .naroop-logout-btn,
  .naroop-account-link,
  .naroop-signup-btn,
  .naroop-login-btn,
  .close-modal-btn {
    border: 3px solid CanvasText !important;
    background: Canvas !important;
    color: CanvasText !important;
    box-shadow: none !important;
  }

  .naroop-story-topic,
  .naroop-stat-card {
    border: 3px solid CanvasText;
    background: Canvas;
    color: CanvasText;
  }

  /* Ensure sufficient contrast for gradients */
  .naroop-hero,
  .naroop-community-stats,
  .home-screen,
  .home-header,
  .naroop-auth-header,
  .naroop-main-nav {
    background: Canvas !important;
    color: CanvasText !important;
    border: 2px solid CanvasText;
  }

  /* High contrast modal backgrounds */
  .confirmation-overlay,
  .naroop-user-profile-modal,
  .admin-dashboard-modal {
    background: rgba(0, 0, 0, 0.9) !important;
  }

  .confirmation-dialog,
  .naroop-user-profile-content,
  .admin-dashboard-content {
    background: Canvas !important;
    color: CanvasText !important;
    border: 3px solid CanvasText !important;
  }
}

/* Home Screen Styles - Optimized Layout */
.home-screen {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #FDFBF5; /* Light cream background */
  min-height: calc(100vh - 200px);
}

/* Primary Section - Hero + Main Action */
.home-primary-section {
  margin-bottom: 40px;
}

.home-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 20px;
  background: #FDFBF5; /* Light cream background */
  color: #591C28; /* Dark maroon text */
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(89, 28, 40, 0.1);
  border: 1px solid rgba(89, 28, 40, 0.1);
}

.home-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
  color: #591C28; /* Dark maroon text */
}

.home-header p {
  font-size: 1.2rem;
  opacity: 0.8;
  margin: 0;
  color: #591C28; /* Dark maroon text */
}

/* Primary Action Section - Story Form Prominent */
.primary-action-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(89, 28, 40, 0.1);
  border: 1px solid rgba(89, 28, 40, 0.1);
  margin-bottom: 30px;
}

.story-form-container {
  max-width: 100%;
}

/* Secondary Actions Section */
.secondary-actions {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 40px;
  box-shadow: 0 4px 15px rgba(89, 28, 40, 0.08);
  border: 1px solid rgba(89, 28, 40, 0.1);
}

.secondary-actions h3 {
  color: #591C28;
  font-size: 1.3rem;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 600;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.quick-action-btn {
  padding: 15px 20px;
  background: transparent;
  border: 2px solid #6E8C65; /* Muted green outline */
  border-radius: 50px; /* Perfect pill shape */
  color: #6E8C65;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(110, 140, 101, 0.1);
  text-align: center;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* Consistent gap for icon-to-text spacing */
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(110, 140, 101, 0.2), transparent);
  transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn:hover {
  background: #6E8C65; /* Green fill on hover */
  color: #FDFBF5;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(110, 140, 101, 0.25);
}

.quick-action-btn:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

.quick-action-btn.primary {
  background: transparent;
  border: 2px solid #F7D046; /* Warm yellow outline for primary action */
  color: #F7D046;
  position: relative;
}

.quick-action-btn.primary::before {
  background: linear-gradient(90deg, transparent, rgba(247, 208, 70, 0.2), transparent);
}

.quick-action-btn.primary:hover {
  background: #F7D046; /* Yellow fill on hover for primary action */
  color: #591C28;
  box-shadow: 0 8px 25px rgba(247, 208, 70, 0.3);
}

.quick-action-btn.primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.quick-action-btn.primary:hover::after {
  width: 100%;
  height: 100%;
  opacity: 0;
}

/* Community Activity Section */
.community-activity-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(89, 28, 40, 0.08);
  border: 1px solid rgba(89, 28, 40, 0.1);
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(89, 28, 40, 0.1);
}

.section-header h2 {
  color: #591C28;
  font-size: 1.8rem;
  margin-bottom: 8px;
  font-weight: 700;
}

.section-header p {
  color: #591C28;
  opacity: 0.7;
  font-size: 1rem;
  margin: 0;
}

/* Content Discovery Controls */
.content-discovery {
  margin-bottom: 25px;
}

/* Mobile Responsive Styles for Home Screen */
@media (max-width: 768px) {
  .home-screen {
    padding: 15px;
  }

  .home-header {
    padding: 25px 15px;
    margin-bottom: 20px;
  }

  .home-header h1 {
    font-size: 2rem;
  }

  .home-header p {
    font-size: 1rem;
  }

  .primary-action-section {
    padding: 20px;
    margin-bottom: 25px;
  }

  .secondary-actions {
    padding: 20px;
    margin-bottom: 30px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .community-activity-section {
    padding: 20px;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .feed-tabs {
    flex-wrap: wrap;
    gap: 8px;
  }

  .feed-tab {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

.feed-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 10px;
}

.feed-tab {
  padding: 10px 20px;
  border: none;
  background: transparent;
  color: #7f8c8d;
  font-weight: 600;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feed-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.2), transparent);
  transition: left 0.5s ease;
}

.feed-tab:hover::before {
  left: 100%;
}

.feed-tab:hover {
  background: #ecf0f1;
  color: #2c3e50;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feed-tab:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

.feed-tab.active {
  background: #3498db;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.feed-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #2980b9, #74b9ff);
  animation: tabGlow 2s ease-in-out infinite alternate;
}

@keyframes tabGlow {
  0% {
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.8);
  }
}

.home-feed {
  margin-top: 20px;
}

.feed-loading {
  text-align: center;
  padding: 40px;
}

.feed-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feed-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid #3498db;
}

.feed-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.feed-item.story-item {
  border-left-color: #e74c3c;
}

.feed-item.support-item {
  border-left-color: #f39c12;
}

.feed-item.discussion-item {
  border-left-color: #9b59b6;
}

.feed-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-type {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7f8c8d;
}

.item-time {
  font-size: 0.8rem;
  color: #bdc3c7;
}

.item-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.item-content {
  color: #5d6d7e;
  line-height: 1.6;
  margin-bottom: 15px;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.tag {
  background: #ecf0f1;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 15px;
}

.action-btn {
  background: none;
  border: none;
  color: #7f8c8d;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.action-btn:hover {
  color: #3498db;
}

.support-details, .discussion-stats {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.support-type, .urgency {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.discussion-stats span {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.empty-feed {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-feed h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.empty-feed p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

.cta-btn {
  background: transparent;
  border: 2px solid #F7D046; /* Warm yellow outline for primary CTA */
  color: #F7D046;
  padding: 12px 24px;
  border-radius: 50px; /* Perfect pill shape */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cta-btn:hover {
  background: #F7D046; /* Yellow fill on hover */
  color: #591C28;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(247, 208, 70, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .home-screen {
    padding: 15px;
    background: #FDFBF5; /* Maintain light cream background on mobile */
  }

  .home-header {
    padding: 30px 15px;
    background: #FDFBF5; /* Light cream background */
    color: #591C28; /* Dark maroon text */
  }

  .home-header h1 {
    font-size: 2rem;
    color: #591C28; /* Dark maroon text */
  }

  .quick-actions {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .quick-action-btn {
    padding: 12px 16px;
    min-height: 44px; /* Ensure touch-friendly size */
    font-size: 0.9rem;
  }

  .feed-tabs {
    flex-wrap: wrap;
    gap: 8px;
  }

  .feed-item {
    padding: 15px;
  }

  /* Ensure header actions stack properly on mobile */
  .header-actions {
    flex-wrap: wrap;
    gap: var(--space-sm);
  }

  .naroop-auth-header {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
  }
}

/* Tablet Responsive Design */
@media (min-width: 768px) and (max-width: 1024px) {
  .home-screen {
    padding: 20px;
    background: #FDFBF5; /* Light cream background */
  }

  .home-header {
    padding: 35px 20px;
    background: #FDFBF5; /* Light cream background */
    color: #591C28; /* Dark maroon text */
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .quick-action-btn {
    padding: 14px 18px;
    min-height: 48px;
    font-size: 1rem;
  }

  .header-actions {
    gap: var(--space-md);
  }

  .naroop-auth-header {
    padding: 1rem 1.5rem;
    flex-direction: row;
    align-items: center;
  }

  /* Ensure buttons maintain proper sizing on tablet */
  .nav-btn {
    padding: var(--space-sm) var(--space-lg);
    min-height: 44px;
  }

  .cancel-btn, .confirm-btn, .submit-btn, .create-campaign-btn, .new-request-btn, .start-discussion-btn {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
  }
}

/* Desktop Responsive Design */
@media (min-width: 1024px) {
  .home-screen {
    background: #FDFBF5; /* Light cream background */
  }

  .home-header {
    background: #FDFBF5; /* Light cream background */
    color: #591C28; /* Dark maroon text */
  }

  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .quick-action-btn {
    min-height: 52px;
    padding: 16px 24px;
  }

  /* Ensure proper spacing for larger screens */
  .header-actions {
    gap: var(--space-lg);
  }

  .naroop-auth-header {
    padding: 1rem 2rem;
  }
}

/* FINAL OVERRIDE: Ensure hamburger menu is completely hidden on larger screens */
@media screen and (min-width: 768px) {
  .naroop-main-nav .nav-toggle,
  nav .nav-toggle,
  button.nav-toggle,
  .nav-toggle {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
  }
}

/* Save Indicator Styles */
.save-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 15px 20px;
  z-index: var(--z-overlay);
  min-width: 200px;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 4px solid #3498db;
}

.save-indicator--visible {
  transform: translateX(0);
}

.save-indicator--hidden {
  transform: translateX(100%);
  opacity: 0;
}

.save-indicator--saving {
  border-left-color: #f39c12;
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.save-indicator--success {
  border-left-color: #27ae60;
  background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.save-indicator--error {
  border-left-color: #e74c3c;
  background: linear-gradient(135deg, #fdeaea 0%, #ffffff 100%);
}

.save-indicator--syncing {
  border-left-color: #9b59b6;
  background: linear-gradient(135deg, #f4f1f8 0%, #ffffff 100%);
}

.save-indicator__content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.save-indicator__icon {
  font-size: 1.2rem;
  animation: saveIconPulse 2s ease-in-out infinite;
}

.save-indicator--saving .save-indicator__icon {
  animation: saveIconSpin 1s linear infinite;
}

.save-indicator__message {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.save-indicator__progress {
  margin-top: 8px;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.save-indicator__progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #f39c12, #e67e22);
  border-radius: 2px;
  animation: saveProgress 2s ease-in-out infinite;
}

@keyframes saveIconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes saveIconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes saveProgress {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

/* Auto-save indicator for forms */
.auto-save-indicator {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-left: 10px;
}

.auto-save-indicator--saving {
  color: #f39c12;
}

.auto-save-indicator--saved {
  color: #27ae60;
}

.auto-save-indicator--error {
  color: #e74c3c;
}

.auto-save-indicator__icon {
  font-size: 0.9rem;
}

.auto-save-indicator--saving .auto-save-indicator__icon {
  animation: saveIconSpin 1s linear infinite;
}

/* Form header styles */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

.form-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .save-indicator {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    transform: translateY(-100%);
  }

  .save-indicator--visible {
    transform: translateY(0);
  }

  .save-indicator--hidden {
    transform: translateY(-100%);
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .auto-save-indicator {
    align-self: flex-end;
  }
}

/* Mobile-Specific Utility Classes */
.mobile-only {
  display: block;
}

.tablet-up {
  display: none;
}

.desktop-only {
  display: none;
}

/* Touch-friendly spacing */
.touch-spacing {
  padding: var(--space-lg);
  margin: var(--space-md) 0;
}

/* Mobile-optimized text */
.mobile-text {
  font-size: max(16px, var(--text-base));
  line-height: 1.5;
}

/* Tablet breakpoint utilities */
@media screen and (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .tablet-up {
    display: block;
  }
}

/* Desktop breakpoint utilities */
@media screen and (min-width: 1024px) {
  .desktop-only {
    display: block;
  }
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(var(--space-md), env(safe-area-inset-left));
    padding-right: max(var(--space-md), env(safe-area-inset-right));
    padding-top: max(var(--space-md), env(safe-area-inset-top));
    padding-bottom: max(var(--space-md), env(safe-area-inset-bottom));
  }
}

/* Extra small screens (320px and below) - optimize for very small devices */
@media (max-width: 320px) {
  .naroop-hero h1 {
    font-size: 1.8rem;
  }

  .naroop-hero h2 {
    font-size: 1rem;
  }

  .naroop-story-form {
    padding: var(--space-md);
    margin: 0 var(--space-xs) var(--space-lg) var(--space-xs);
    width: calc(100% - 2 * var(--space-xs));
  }

  .nav-btn {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-sm);
  }

  .naroop-story-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .naroop-reaction-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Improved touch targets for all interactive elements */
button,
input[type="button"],
input[type="submit"],
input[type="reset"],
a,
[role="button"],
[tabindex="0"] {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
}

/* Ensure form inputs are touch-friendly */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
  min-height: var(--touch-target-min);
  font-size: max(16px, 1rem); /* Prevents zoom on iOS */
}

/* Admin Debug Helper Styles */
.admin-test-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-debug);
  padding: 20px;
}

.admin-test-container {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.admin-test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.admin-test-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.close-test-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-test-btn:hover {
  background: #c82333;
}

.admin-test-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.admin-test-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
  text-align: center;
}

.close-test-btn-large {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
}

.close-test-btn-large:hover {
  background: #5a6268;
}

.debug-section {
  margin-bottom: 20px;
}

.debug-section h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}
